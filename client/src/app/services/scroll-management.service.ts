import { Injectable, inject, PLATFORM_ID, signal, computed } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { ViewportScroller } from '@angular/common';
import { isPlatformBrowser } from '@angular/common';
import { filter, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { Subject, BehaviorSubject } from 'rxjs';

export interface ScrollPosition {
  x: number;
  y: number;
}

export interface DataLoadingOperation {
  id: string;
  type: 'pagination' | 'infinite-scroll' | 'load-more' | 'search' | 'filter';
  preserveScroll: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ScrollManagementService {
  private router = inject(Router);
  private viewportScroller = inject(ViewportScroller);
  private platformId = inject(PLATFORM_ID);

  // Signals for reactive state management
  private dataLoadingOperations = signal<Map<string, DataLoadingOperation>>(new Map());
  private currentScrollPosition = signal<ScrollPosition>({ x: 0, y: 0 });
  private lastNavigationTime = signal<number>(0);

  // Computed signals
  public isDataLoading = computed(() => this.dataLoadingOperations().size > 0);
  public shouldPreserveScroll = computed(() => {
    const operations = Array.from(this.dataLoadingOperations().values());
    return operations.some(op => op.preserveScroll);
  });

  // Subjects for internal communication
  private scrollPositionSubject = new BehaviorSubject<ScrollPosition>({ x: 0, y: 0 });
  private dataLoadingSubject = new Subject<{ operation: DataLoadingOperation; action: 'start' | 'end' }>();

  // Configuration
  private readonly SCROLL_DEBOUNCE_TIME = 100;
  private readonly NAVIGATION_SCROLL_DELAY = 50;

  constructor() {
    this.initializeScrollManagement();
  }

  /**
   * Initialize scroll management system
   */
  private initializeScrollManagement(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    // Listen to router navigation events
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        debounceTime(this.NAVIGATION_SCROLL_DELAY)
      )
      .subscribe(() => {
        this.handleNavigationEnd();
      });

    // Track scroll position changes
    this.trackScrollPosition();
  }

  /**
   * Handle navigation end events
   */
  private handleNavigationEnd(): void {
    this.lastNavigationTime.set(Date.now());

    // Small delay to allow component initialization
    setTimeout(() => {
      if (!this.shouldPreserveScroll()) {
        this.scrollToTop();
      }
    }, this.NAVIGATION_SCROLL_DELAY);
  }

  /**
   * Track current scroll position
   */
  private trackScrollPosition(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const trackScroll = () => {
      const position: ScrollPosition = {
        x: window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0,
        y: window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0
      };
      
      this.currentScrollPosition.set(position);
      this.scrollPositionSubject.next(position);
    };

    // Throttled scroll tracking
    let ticking = false;
    const scrollHandler = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          trackScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', scrollHandler, { passive: true });
  }

  /**
   * Mark the start of a data loading operation
   */
  public markDataLoadingStart(operation: Omit<DataLoadingOperation, 'preserveScroll'>): void {
    const fullOperation: DataLoadingOperation = {
      ...operation,
      preserveScroll: this.shouldPreserveScrollForOperation(operation.type)
    };

    this.dataLoadingOperations.update(operations => {
      const newOperations = new Map(operations);
      newOperations.set(operation.id, fullOperation);
      return newOperations;
    });

    this.dataLoadingSubject.next({ operation: fullOperation, action: 'start' });
  }

  /**
   * Mark the end of a data loading operation
   */
  public markDataLoadingEnd(operationId: string): void {
    const operation = this.dataLoadingOperations().get(operationId);
    
    if (operation) {
      this.dataLoadingOperations.update(operations => {
        const newOperations = new Map(operations);
        newOperations.delete(operationId);
        return newOperations;
      });

      this.dataLoadingSubject.next({ operation, action: 'end' });
    }
  }

  /**
   * Determine if scroll should be preserved for operation type
   */
  private shouldPreserveScrollForOperation(type: DataLoadingOperation['type']): boolean {
    switch (type) {
      case 'pagination':
      case 'infinite-scroll':
      case 'load-more':
        return true;
      case 'search':
      case 'filter':
        return false; // Usually want to scroll to top for new search/filter results
      default:
        return false;
    }
  }

  /**
   * Scroll to top of the page
   */
  public scrollToTop(behavior: ScrollBehavior = 'smooth'): void {
    if (!isPlatformBrowser(this.platformId)) return;

    // Use ViewportScroller for better SSR compatibility
    this.viewportScroller.scrollToPosition([0, 0]);
    
    // Fallback for immediate scrolling
    if (behavior === 'auto') {
      window.scrollTo({ top: 0, left: 0, behavior: 'auto' });
    }
  }

  /**
   * Scroll to specific position
   */
  public scrollToPosition(position: ScrollPosition, behavior: ScrollBehavior = 'smooth'): void {
    if (!isPlatformBrowser(this.platformId)) return;

    this.viewportScroller.scrollToPosition([position.x, position.y]);
    
    if (behavior === 'auto') {
      window.scrollTo({ top: position.y, left: position.x, behavior: 'auto' });
    }
  }

  /**
   * Get current scroll position
   */
  public getCurrentScrollPosition(): ScrollPosition {
    return this.currentScrollPosition();
  }

  /**
   * Check if currently in data loading state
   */
  public isCurrentlyDataLoading(): boolean {
    return this.isDataLoading();
  }

  /**
   * Get observable for scroll position changes
   */
  public getScrollPosition$() {
    return this.scrollPositionSubject.asObservable().pipe(
      distinctUntilChanged((prev, curr) => prev.x === curr.x && prev.y === curr.y),
      debounceTime(this.SCROLL_DEBOUNCE_TIME)
    );
  }

  /**
   * Get observable for data loading state changes
   */
  public getDataLoadingState$() {
    return this.dataLoadingSubject.asObservable();
  }

  /**
   * Force scroll to top (ignores data loading state)
   */
  public forceScrollToTop(): void {
    this.scrollToTop('auto');
  }

  /**
   * Temporarily disable scroll management for specific operation
   */
  public withScrollPreservation<T>(operation: () => T): T {
    const tempId = `temp-${Date.now()}`;
    this.markDataLoadingStart({ id: tempId, type: 'pagination' });
    
    try {
      return operation();
    } finally {
      // Clean up after a short delay to ensure scroll preservation
      setTimeout(() => {
        this.markDataLoadingEnd(tempId);
      }, 100);
    }
  }
}
