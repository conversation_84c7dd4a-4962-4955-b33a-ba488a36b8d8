import {inject, Injectable, makeStateKey, signal, TransferState} from '@angular/core';
import {Meta, Title} from "@angular/platform-browser";
import {TranslocoService} from "@jsverse/transloco";
import {HttpClient, HttpErrorResponse, HttpParams} from "@angular/common/http";
import {IContent} from "@/interfaces/content";
import {tap, catchError, of, Observable, EMPTY, Subject} from "rxjs";
import {ProfileService} from "@/services/profile.service";
import { ShareDataService } from './share-data.service';
import { ScrollManagementService } from './scroll-management.service';

const META_DATA_KEY = makeStateKey<any>('meta-data');

@Injectable({
  providedIn: 'root'
})
export class ContentService {
  profileService = inject(ProfileService);
  translocoService = inject(TranslocoService)
  transferState = inject(TransferState)
  http = inject(HttpClient)
  title = inject(Title)
  shareDataService = inject(ShareDataService)
  meta = inject(Meta)
  scrollManagementService = inject(ScrollManagementService)
  res = signal<IContent | null>(null)
  resPreview = signal<IContent | null>(null)
  list = signal<IContent[]>([])
  similar: any = signal([])
  lang: any = null
  page: any = null
  totalPages = signal<number>(1) // Add this signal for total pages
  currentPage = signal<number>(1) // Add this signal for current page
  isLoading = signal<boolean>(false) // Add loading state signal

  // Subject to emit filter results for toast notifications
  private filterResultsSubject = new Subject<{total: number, isFilterOperation: boolean}>();
  filterResults$ = this.filterResultsSubject.asObservable();

  // Flag to track if the current request is a filter operation
  private isFilterOperation = false;



  get(id: number) {
    return this.http.get('/client/content/link/' + id)
  }

  getAll(filter: any = null, loadMore: boolean = false) {
    this.isLoading.set(true);

    // Generate unique operation ID for scroll management
    const operationId = `content-${loadMore ? 'load-more' : 'filter'}-${Date.now()}`;

    // Mark data loading operation for scroll management
    if (loadMore) {
      this.scrollManagementService.markDataLoadingStart({
        id: operationId,
        type: 'load-more'
      });
    } else if (this.isFilterOperation) {
      this.scrollManagementService.markDataLoadingStart({
        id: operationId,
        type: 'filter'
      });
    }

    // If loading more, increment the page, otherwise reset to page 1
    if (loadMore) {
      this.currentPage.set(this.currentPage() + 1);
    } else {
      this.currentPage.set(1);
    }

    // Update the filter with the current page
    const updatedFilter = { ...filter, page: this.currentPage(), lang: this.translocoService.getActiveLang() };

    let params = new HttpParams();
    Object.keys(updatedFilter).forEach(key => {
      if (updatedFilter[key] !== undefined && updatedFilter[key] !== null && updatedFilter[key] != '') {
        params = params.append(key, updatedFilter[key].toString());
      }
    });

    return this.http.get('/client/content', {params}).pipe(
      tap((res: any) => {
        // Update total pages from response
        this.totalPages.set(res.pagination.totalPages || 1);

        // If loading more, append to existing list, otherwise replace
        if (loadMore) {
          this.list.update(currentList => [...currentList, ...res.items]);
        } else {
          this.list.set(res.items);

          // Emit filter results if this was a filter operation (not loadMore)
          if (this.isFilterOperation) {
            this.filterResultsSubject.next({
              total: res.pagination.total,
              isFilterOperation: true
            });
            // Reset the flag
            this.isFilterOperation = false;
          }
        }

        this.isLoading.set(false);

        // Mark data loading operation as complete
        this.scrollManagementService.markDataLoadingEnd(operationId);
      }),
      catchError(error => {
        console.error('Error fetching content:', error);
        this.isLoading.set(false);

        // Mark data loading operation as complete even on error
        this.scrollManagementService.markDataLoadingEnd(operationId);

        return of({ items: [], totalPages: 0 });
      })
    );
  }

  // Add a method to check if there are more pages
  hasMorePages(): boolean {
    return this.currentPage() < this.totalPages();
  }

  // Method to set filter operation flag
  setFilterOperation(isFilterOperation: boolean = true) {
    this.isFilterOperation = isFilterOperation;
  }

  getContent(lang: string, page: string, views: boolean = true) {
    return this.http.get('/client/content/' + page, {params: {lang, views}})
  }

  getContentPreview(lang: string, page: string, views: boolean = false): Observable<any> {
    return this.http.get('/client/content/' + page, {params: {lang, views}, withCredentials: true})
  }

  addToFavourites(id: number) {
    return this.http.post('/client/content/favourite', { id }).pipe(
      catchError(error => {
        this.shareDataService.showInfoModal(error.error.message);
        return EMPTY;
      })
    )
  }

  like(id: number) {
    return this.http.post('/client/content/like', {id}).pipe(
      catchError(error => {
        this.shareDataService.showInfoModal(error.error.message);
        return EMPTY;
      })
    )
  }

  getCategories() {
    return this.http.get('/client/content/categories')
  }

  addQuoteToFavourites(id: number, quote: string, share = false) {
    return this.http.post('/client/content/quote', {id, quote, share})
  }

  getQuote(id: number) {
    return this.http.get('/client/content/quote', {params: {id}})
  }

  getLikes(slug: string) {
    return this.http.get('/client/content/likes', {params: {slug}})
  }

  getFavourites() {
    return this.http.get('/client/content/favourites')
  }

  getFavouritesByIds(ids: number[], page = 1) {
    return this.http.get('/client/content/favourites/ids', {params: {ids, page}})
  }

  getSimilar(slug: string) {
    return this.http.get('/client/content/similar', {params: {slug}})
  }
}
