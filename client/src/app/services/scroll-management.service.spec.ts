import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ViewportScroller } from '@angular/common';
import { PLATFORM_ID } from '@angular/core';
import { ScrollManagementService } from './scroll-management.service';

describe('ScrollManagementService', () => {
  let service: ScrollManagementService;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockViewportScroller: jasmine.SpyObj<ViewportScroller>;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['events']);
    const viewportScrollerSpy = jasmine.createSpyObj('ViewportScroller', ['scrollToPosition']);

    TestBed.configureTestingModule({
      providers: [
        ScrollManagementService,
        { provide: Router, useValue: routerSpy },
        { provide: ViewportScroller, useValue: viewportScrollerSpy },
        { provide: PLATFORM_ID, useValue: 'browser' }
      ]
    });

    service = TestBed.inject(ScrollManagementService);
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockViewportScroller = TestBed.inject(ViewportScroller) as jasmine.SpyObj<ViewportScroller>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should track data loading operations', () => {
    const operation = { id: 'test-1', type: 'pagination' as const };
    
    service.markDataLoadingStart(operation);
    expect(service.isCurrentlyDataLoading()).toBe(true);
    
    service.markDataLoadingEnd('test-1');
    expect(service.isCurrentlyDataLoading()).toBe(false);
  });

  it('should preserve scroll for pagination operations', () => {
    const operation = { id: 'test-pagination', type: 'pagination' as const };
    
    service.markDataLoadingStart(operation);
    expect(service.shouldPreserveScroll()).toBe(true);
  });

  it('should not preserve scroll for search operations', () => {
    const operation = { id: 'test-search', type: 'search' as const };
    
    service.markDataLoadingStart(operation);
    expect(service.shouldPreserveScroll()).toBe(false);
  });

  it('should scroll to top when called', () => {
    service.scrollToTop();
    expect(mockViewportScroller.scrollToPosition).toHaveBeenCalledWith([0, 0]);
  });

  it('should handle multiple concurrent operations', () => {
    const op1 = { id: 'test-1', type: 'pagination' as const };
    const op2 = { id: 'test-2', type: 'load-more' as const };
    
    service.markDataLoadingStart(op1);
    service.markDataLoadingStart(op2);
    expect(service.isCurrentlyDataLoading()).toBe(true);
    
    service.markDataLoadingEnd('test-1');
    expect(service.isCurrentlyDataLoading()).toBe(true);
    
    service.markDataLoadingEnd('test-2');
    expect(service.isCurrentlyDataLoading()).toBe(false);
  });
});
