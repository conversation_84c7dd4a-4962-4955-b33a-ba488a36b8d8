# Global Scroll Management System

This document describes the global scroll management system implemented for the Angular SPA application.

## Overview

The scroll management system provides:
1. **Default behavior**: Automatically scroll to top when navigating between routes
2. **Exception handling**: Preserve scroll position during data loading operations (pagination, infinite scroll, load more)
3. **Global implementation**: Works across all routes in the SPA

## Architecture

### ScrollManagementService

The core service that manages scroll behavior throughout the application.

**Key Features:**
- Signal-based reactive state management
- Platform-aware (SSR compatible)
- Automatic router integration
- Debounced scroll tracking
- Operation-specific scroll preservation

### Router Configuration

Updated `app.config.ts` to use `scrollPositionRestoration: 'top'` for default scroll-to-top behavior.

## Usage

### Automatic Integration

The system automatically handles:
- Route navigation (scrolls to top)
- Data loading operations marked by services

### Manual Integration

For custom data loading operations:

```typescript
import { ScrollManagementService } from './services/scroll-management.service';

export class MyComponent {
  scrollManagementService = inject(ScrollManagementService);

  loadMoreData() {
    // Mark start of data loading operation
    const operationId = `my-operation-${Date.now()}`;
    this.scrollManagementService.markDataLoadingStart({
      id: operationId,
      type: 'load-more' // or 'pagination', 'infinite-scroll', 'search', 'filter'
    });

    this.dataService.loadMore().subscribe({
      next: (data) => {
        // Handle data
      },
      complete: () => {
        // Mark end of operation
        this.scrollManagementService.markDataLoadingEnd(operationId);
      }
    });
  }
}
```

### Operation Types

- `pagination`: Preserves scroll position (e.g., page navigation)
- `infinite-scroll`: Preserves scroll position
- `load-more`: Preserves scroll position
- `search`: Does not preserve scroll (scrolls to top for new results)
- `filter`: Does not preserve scroll (scrolls to top for new results)

### Utility Methods

```typescript
// Force scroll to top (ignores data loading state)
this.scrollManagementService.forceScrollToTop();

// Get current scroll position
const position = this.scrollManagementService.getCurrentScrollPosition();

// Check if data loading is in progress
const isLoading = this.scrollManagementService.isCurrentlyDataLoading();

// Temporarily preserve scroll for an operation
this.scrollManagementService.withScrollPreservation(() => {
  // Your operation here
});
```

## Integrated Services

The following services are already integrated with scroll management:

### ContentService
- `getAll()` method with `loadMore` parameter
- Automatically marks pagination and filter operations

### SearchService
- `search()` method with `isLoadMore` parameter
- Distinguishes between new searches and pagination

## Implementation Details

### Signal-Based State Management

The service uses Angular signals for reactive state management:

```typescript
// Reactive state
public isDataLoading = computed(() => this.dataLoadingOperations().size > 0);
public shouldPreserveScroll = computed(() => {
  const operations = Array.from(this.dataLoadingOperations().values());
  return operations.some(op => op.preserveScroll);
});
```

### Router Integration

Automatically listens to `NavigationEnd` events and scrolls to top unless data loading is in progress:

```typescript
this.router.events
  .pipe(filter(event => event instanceof NavigationEnd))
  .subscribe(() => {
    if (!this.shouldPreserveScroll()) {
      this.scrollToTop();
    }
  });
```

### Platform Compatibility

All scroll operations are platform-aware and work with SSR:

```typescript
if (!isPlatformBrowser(this.platformId)) return;
```

## Testing

The service includes comprehensive unit tests covering:
- Data loading operation tracking
- Scroll preservation logic
- Multiple concurrent operations
- Platform compatibility

Run tests with:
```bash
ng test --include="**/scroll-management.service.spec.ts"
```

## Best Practices

1. **Always mark data loading operations** that should preserve scroll position
2. **Use appropriate operation types** to get correct scroll behavior
3. **Clean up operations** by calling `markDataLoadingEnd()` in both success and error cases
4. **Use `withScrollPreservation()`** for simple one-off operations
5. **Test scroll behavior** when implementing new data loading features

## Troubleshooting

### Scroll not preserved during pagination
- Ensure the operation is marked with `markDataLoadingStart()`
- Check that the operation type supports scroll preservation
- Verify `markDataLoadingEnd()` is called after completion

### Scroll to top not working on navigation
- Check that no data loading operations are active
- Verify router configuration in `app.config.ts`
- Ensure the service is properly injected

### Performance issues
- The service uses throttled scroll tracking and debounced operations
- All scroll listeners use `{ passive: true }` for better performance
- Operations are automatically cleaned up to prevent memory leaks
