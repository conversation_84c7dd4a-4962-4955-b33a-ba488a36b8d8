import {inject, Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {tap} from "rxjs";
import {SearchTabTypes} from "@/components/search/search.component";
import { ScrollManagementService } from './scroll-management.service';

@Injectable({
  providedIn: 'root'
})
export class SearchService {
  http: HttpClient = inject(HttpClient)
  scrollManagementService = inject(ScrollManagementService)

  search(query: string, page: number, tab: SearchTabTypes, isLoadMore: boolean = false) {
    // Generate unique operation ID for scroll management
    const operationId = `search-${isLoadMore ? 'load-more' : 'new'}-${Date.now()}`;

    // Mark data loading operation for scroll management
    this.scrollManagementService.markDataLoadingStart({
      id: operationId,
      type: isLoadMore ? 'pagination' : 'search'
    });

    return this.http.get('/client/search', {params: {search: query, page, tab}}).pipe(
      tap(() => {
        // Mark data loading operation as complete
        this.scrollManagementService.markDataLoadingEnd(operationId);
      })
    );
  }
}
