import { RouterOutlet, Router, ActivatedRoute, RouterLink, NavigationEnd } from '@angular/router';
import { Component, ElementRef, EventEmitter, inject, OnInit, Output, Renderer2, signal, ViewChild } from '@angular/core';
import { CommonModule, DOCUMENT, isPlatformBrowser, Location, NgOptimizedImage } from '@angular/common';
import { MainPlayerComponent } from './components/main-player/main-player.component';
import { ToasterComponent } from './components/toaster/toaster.component';
import { HeaderComponent } from './components/header/header.component';
import { ClickOutsideDirective } from "@/directives/clickOutside";
import { ToasterService } from './services/toaster.service';
import { Inject, PLATFORM_ID } from '@angular/core';
import { ScrollManagementService } from './services/scroll-management.service';
import { TranslocoService } from "@jsverse/transloco";
import { ThemeEnum } from './enums/theme';
import { filter } from 'rxjs';
import { FooterComponent } from './components/footer/footer.component';
import { ProfileService } from './services/profile.service';
import { AuthService } from './services/auth.service';
import { SsrCookieService } from 'ngx-cookie-service-ssr';
import { ShareDataService } from './services/share-data.service';
import { Subject, takeUntil } from 'rxjs';
import { SpinnerService } from './services/spinner.service';
import { TranslationService } from './services/translation.service';
import { environment } from "@/env/environment";


@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    RouterOutlet,
    MainPlayerComponent,
    HeaderComponent,
    FooterComponent,
    ToasterComponent,
    ClickOutsideDirective,
    NgOptimizedImage
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  environment = environment;
  route = inject(ActivatedRoute);
  router = inject(Router);
  translocoService = inject(TranslocoService);
  translationService = inject(TranslationService);
  profileService = inject(ProfileService);
  authService = inject(AuthService);
  cookieService = inject(SsrCookieService)
  shareDataService = inject(ShareDataService)
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  message: string = "";
  @Output() selectedThemeChanged = new EventEmitter<ThemeEnum>();
  isSidebarOpen: boolean = false;
  themeToSet: string = ThemeEnum.Light;
  isMenuOpened: any = {};
  theme: string = '';
  lightThemeColors: Array<any> = [
    { name: '--main-back-gradient', value: 'linear-gradient(180deg, #F8C760 -13.12%, #CC8630 53.7%, #F1B94F 115.63%)' },
    { name: '--main-back-stripes', value: 'url(assets/images/strips_long.svg)' },
    { name: '--venz_', value: 'url(assets/images/venz_.png)' },
    { name: '--venz_cover', value: 'url(assets/images/ssr.png)' },
    { name: '--m-burg', value: 'url(assets/images/icons/m-burg.svg)' },
    { name: '--m-books', value: 'url(assets/images/icons/m-books.svg)' },
    { name: '--m-prs', value: 'url(assets/images/icons/m-prs.svg)' },
    { name: '--m-srch', value: 'url(assets/images/icons/m-srch.svg)' },
    { name: '--m-pl', value: 'url(assets/images/icons/m-pl.svg)' },
    { name: '--m-pl-swch', value: 'url(assets/images/icons/m-pl-swch.svg)' },
    { name: '--no-repeat', value: 'url(assets/images/icons/no-repit.svg)' },
    { name: '--all-repeat', value: 'url(assets/images/icons/all-repeat.svg)' },
    { name: '--one-repeat', value: 'url(assets/images/icons/one-repeat.svg)' },
    { name: '--main-back-stripes_md', value: 'url(assets/images/strips_long_md.svg)' },
    { name: '--shoffl', value: 'url(assets/images/icons/shoffl.svg)' },
    { name: '--no-shfl', value: 'url(assets/images/icons/no_shfl.svg)' },
    { name: '--30sback', value: 'url(assets/images/icons/30sback.svg)' },
    { name: '--30sforw', value: 'url(assets/images/icons/30sff.svg)' },
    { name: '--side-back-stripes', value: 'url(assets/images/top_strips.svg)' },
    { name: '--close_butn', value: 'url(assets/images/icons/close.svg)' },
    { name: '--1speed', value: 'url(assets/images/icons/1x.svg)' },
    { name: '--l-heart', value: 'url(assets/images/icons/l-heart.svg)' },
    { name: '--lk-heart', value: 'url(assets/images/icons/lk-heart.svg)' },
    { name: '--scroll_top', value: 'url(assets/images/icons/up_.svg)' },
    { name: '--l-star', value: 'url(assets/images/icons/l-star.svg)' },
    { name: '--l-share', value: 'url(assets/images/icons/l-share.svg)' },
    { name: '--add-p', value: 'url(assets/images/icons/add-p.svg)' },
    { name: '--listt_', value: 'url(assets/images/icons/listt_.svg)' },
    { name: '--grey-back', value: '#e5e7eb' },
    { name: '--dr-back', value: '#5d5e60' },
    { name: '--drf-back', value: '#cec6c6' },
    { name: '--main-text', value: '#000' },
    { name: '--main-background-image', value: 'url(assets/images/Pat_50.svg), url(assets/images/side_gradient.svg)' },
    { name: '--top-cover', value: 'url(assets/images/gradient.svg)' },
    { name: '--cover_telegram', value: 'url(assets/images/icons/telg.svg)' },
    { name: '--cover_instagram', value: 'url(assets/images/icons/instg.svg)' },
    { name: '--cover_phone', value: 'url(assets/images/icons/phone.svg)' },
    { name: '--cover_email', value: 'url(assets/images/icons/mail.svg)' },
    { name: '--home-image', value: 'url(assets/images/icons/home.svg)' },
    { name: '--heart_dark', value: 'url(assets/images/icons/like_dark.svg)' },
    { name: '--button_figure', value: 'url(assets/images/icons/button_hover.svg)' },
    { name: '--button_', value: 'url(assets/images/icons/button_.svg)' },
    { name: '--star_dark', value: 'url(assets/images/icons/star_dark.svg)' },
    { name: '--arrow-right', value: 'url(assets/images/icons/arrow.svg)' },
    { name: '--font-color', value: 'rgba(83, 46, 0, 1)' },
    { name: '--font-color1', value: 'rgba(83, 46, 0, 1)' },
    { name: '--text-color', value: 'rgba(222, 165, 61, 1)' },
    { name: '--light-color', value: 'rgba(249, 233, 200, 1)' },
    { name: '--text-color23', value: 'rgba(83, 46, 0, 1)' },
    { name: '--calendar', value: 'url(assets/images/icons/calendar.svg)' },
    { name: '--clock', value: 'url(assets/images/icons/clock.svg)' },
    { name: '--heart', value: 'url(assets/images/icons/heart.svg)' },
    { name: '--star', value: 'url(assets/images/icons/star.svg)' },
    { name: '--share', value: 'url(assets/images/icons/share.svg)' },
    { name: '--content', value: 'url(assets/images/icons/content.svg)' },
    { name: '--selection', value: 'rgba(255, 226, 163, 1)' },
    { name: '--check', value: 'url(assets/images/icons/chck.svg)' },
    { name: '--border', value: 'rgba(243, 213, 147, 1)' },
    { name: '--side_back', value: 'rgba(253, 237, 201, 1)' },
    { name: '--shadow', value: 'rgba(83, 46, 0, 0.6)' },
    { name: '--blockquote-par', value: 'rgba(211, 146, 60, 1)' },
    { name: '--blockquote-before', value: 'url(assets/images/icons/trishul.svg)' },
    { name: '--blockquote-after', value: 'url(assets/images/icons/line.svg)' },
    { name: '--blockquote-after_md', value: 'url(assets/images/icons/line_md.svg)' },
    { name: '--copy_', value: 'url(assets/images/icons/copy_highlight.svg)' },
    { name: '--shr_', value: 'url(assets/images/icons/share_highlight.svg)' },
    { name: '--add_', value: 'url(assets/images/icons/fav_highlight.svg)' },
    { name: '--srch_', value: 'url(assets/images/icons/search_highlight.svg)' },
    { name: '--burger', value: 'url(assets/images/icons/burg.svg)' },
    { name: '--arrow_d', value: 'url(assets/images/icons/arr_dd.svg)' },
    { name: '--arrow_w', value: 'url(assets/images/icons/arr_w_.svg)' },
    { name: '--triangl_w', value: 'url(assets/images/icons/triangl_.svg)' },
    { name: '--buttn_catg', value: 'url(assets/images/icons/buttn_catg.svg)' },
    { name: '--menu_font', value: 'rgba(255, 255, 255, 1)' },
    { name: '--serch', value: 'url(assets/images/icons/serch.svg)' },
    { name: '--btt_top', value: 'url(assets/images/icons/btt_top.svg)' },
    { name: '--signin', value: 'url(assets/images/icons/signin.svg)' },
    { name: '--authorized', value: 'url(assets/images/icons/ava_outer.svg)' },
    { name: '--circl_lang', value: 'url(assets/images/icons/circle.svg)' },
    { name: '--circl_lt', value: 'url(assets/images/icons/light.svg)' },
    { name: '--circl_md', value: 'url(assets/images/icons/bw.svg)' },
    { name: '--circl_dk', value: 'url(assets/images/icons/dark.svg)' },
    { name: '--lan_switch', value: 'url(assets/images/icons/lan_switch.svg)' },
    { name: '--lan_btn', value: 'url(assets/images/icons/lang.svg)' },
    { name: '--ar', value: 'url(assets/images/icons/ar_ligt.svg)' },
    { name: '--bf_gradient', value: 'linear-gradient(180deg, #F1B94F 0%, rgba(255, 226, 163, 0) 100%)' },
    { name: '--blur_sec', value: 'rgba(137, 87, 10, 0.5)' },
    { name: '--blur_main', value: 'rgba(83, 46, 0, 0.55)' },
    { name: '--btn_main', value: 'url(assets/images/icons/btr_reg.svg)' },
    { name: '--btn_main_r', value: 'url(assets/images/icons/btr_reg_b.svg)' },
    { name: '--t1', value: 'url(assets/images/icons/t1.svg)' },
    { name: '--y1', value: 'url(assets/images/icons/yt1.svg)' },
    { name: '--v1', value: 'url(assets/images/icons/vk1.svg)' },
    { name: '--f1', value: 'url(assets/images/icons/f1.svg)' },
    { name: '--i1', value: 'url(assets/images/icons/i1_blue.svg)' },
    { name: '--t2', value: 'url(assets/images/icons/t2.svg)' },
    { name: '--m1', value: 'url(assets/images/icons/m1.svg)' },
    { name: '--bt', value: 'url(assets/images/bt_.svg)' },
    { name: '--x_bt', value: 'url(assets/images/icons/x_.svg)' },
    { name: '--x_a', value: 'url(assets/images/icons/x_a.svg)' },
    { name: '--arrs_sh', value: 'url(assets/images/icons/arrs_.svg)' },
    { name: '--lib-after', value: 'url(assets/images/l_line.svg)' },
    { name: '--tab_nominal', value: 'url(assets/images/tab_noml.svg)' },
    { name: '--tab_active', value: 'url(assets/images/tab_selected.svg)' },
    { name: '--lib-after_md', value: 'url(assets/images/lib-after_md.svg)' },
    { name: '--book_about', value: '#dea53d' },
    { name: '--buttn_catg2', value: 'url(assets/images/buttn_catg2.svg)' },
    { name: '--om_main', value: 'url(assets/images/icons/om_main.svg)' },
    { name: '--nav_cont_sch', value: 'url(assets/images/icons/search_md.svg)' },
    { name: '--burger_md', value: 'url(assets/images/icons/burg_md.svg)' },
    { name: '--pl_bg', value: 'url(assets/images/icons/pl_bg.svg)' },
    { name: '--ps_bg', value: 'url(assets/images/icons/ps_bg.svg)' },
    { name: '--d_line', value: '#F3D593' },
    { name: '--p_line', value: '#9A6119' },
    { name: '--d_back', value: 'rgb(231 174 78)' },
    { name: '--tri_', value: 'url(assets/images/icons/tri_.svg)' },
    { name: '--pl_start', value: 'rgba(255, 226, 163, 1)' },
    { name: '--pl_stop', value: 'rgba(240, 187, 86, 1)' },
    { name: '--pl_start1', value: '#E1A644' },
    { name: '--pl_stop1', value: '#9A6119' },
    { name: '--pl_start2', value: '#B87E2B' },
    { name: '--pl_stop2', value: '#86510E' },
    { name: '--pl_line', value: 'rgba(243, 213, 147, 1)' },
    { name: '--pl_line1', value: 'rgba(243, 213, 147, 1)' },
    { name: '--pl-back-gradient', value: 'linear-gradient(90deg, #DEA53D 0%, #D08C33 50%, #F0BB56 100%)' },
    { name: '--line_back', value: 'rgba(154, 97, 25, 1)' },
    { name: '--line_back1', value: 'rgba(154, 97, 25, 1)' },
    { name: '--st_back', value: 'rgba(154, 97, 25, 1)' },
    { name: '--line_handle', value: 'rgba(255, 222, 153, 1)' },
    { name: '--line_handle1', value: 'rgba(225, 166, 68, 1)' },
    { name: '--before_handle', value: '#d39135' },
    { name: '--btt_gradient', value: 'linear-gradient(to right, #fff, #fff), linear-gradient(to right, #FCE1B3, #D19036)' },
    { name: '--cursor-move', value: 'url(assets/images/icons/movee_.svg)' },
    { name: '--menu_font1', value: 'rgba(137, 87, 10, 1)' },
    { name: '--menu_font2', value: '#fff' },
    { name: '--nav-pl', value: 'url(assets/images/icons/nav-pl.svg)' },

    { name: '--button_hover_bg_sm', value: 'url(assets/images/button/button-hover-bg-gold-sm.webp)' },
    { name: '--button_hover_bg_md', value: 'url(assets/images/button/button-hover-bg-gold-md.webp)' },
    { name: '--button_hover_bg_lg', value: 'url(assets/images/button/button-hover-bg-gold-lg.webp)' },
    { name: '--button_hover_bg_xl', value: 'url(assets/images/button/button-hover-bg-gold-xl.webp)' },
    { name: '--button_hover_bg_xxl', value: 'url(assets/images/button/button-hover-bg-gold-xxl.webp)' },
    { name: '--button_hover_bg_xxxl', value: 'url(assets/images/button/button-hover-bg-gold-xxxl.webp)' },
    { name: '--button_sm', value: 'url(assets/images/button/button-bg-sm.webp)' },
    { name: '--button_md', value: 'url(assets/images/button/button-bg-md.webp)' },
    { name: '--button_lg', value: 'url(assets/images/button/button-bg-lg.webp)' },
    { name: '--button_xl', value: 'url(assets/images/button/button-bg-xl.webp)' },
    { name: '--button_xxl', value: 'url(assets/images/button/button-bg-xxl.webp)' },
    { name: '--button_xxxl', value: 'url(assets/images/button/button-bg-xxl.webp)' },
    { name: '--ard', value: 'url(assets/images/icons/ard.svg)' },
  ];
  lightBlueThemeColors: Array<any> = [
    { name: '--grey-back', value: '#000' },
    { name: '--dr-back', value: '#5d5e60' },
    { name: '--drf-back', value: '#cec6c6' },
    { name: '--main-text', value: '#000' },
    { name: '--main-background-image', value: 'url(assets/images/Pat_50.svg), url(assets/images/side_gradient_blue.svg)' },
    { name: '--top-cover', value: 'url(assets/images/gradient_blue.svg)' },
    { name: '--cover_telegram', value: 'url(assets/images/icons/telg_blue.svg)' },
    { name: '--cover_instagram', value: 'url(assets/images/icons/instg_blue.svg)' },
    { name: '--cover_phone', value: 'url(assets/images/icons/phone_blue.svg)' },
    { name: '--cover_email', value: 'url(assets/images/icons/mail_blue.svg)' },
    { name: '--home-image', value: 'url(assets/images/icons/home_blue.svg)' },
    { name: '--heart_dark', value: 'url(assets/images/icons/like_dark_blue.svg)' },
    { name: '--button_figure', value: 'url(assets/images/icons/button_hover_blue.svg)' },
    { name: '--button_', value: 'url(assets/images/icons/button_.svg)' },
    { name: '--star_dark', value: 'url(assets/images/icons/star_dark_blue.svg)' },
    { name: '--arrow-right', value: 'url(assets/images/icons/arrow_blue.svg)' },
    { name: '--font-color', value: 'rgba(42, 124, 187, 1)' },
    { name: '--font-color1', value: 'rgba(0, 57, 100, 1)' },
    { name: '--text-color', value: 'rgba(42, 124, 187, 1)' },
    { name: '--light-color', value: 'rgba(230, 250, 255, 1)' },
    { name: '--text-color23', value: 'rgba(0, 57, 100, 1)' },
    { name: '--calendar', value: 'url(assets/images/icons/calendar_blue.svg)' },
    { name: '--clock', value: 'url(assets/images/icons/clock_blue.svg)' },
    { name: '--heart', value: 'url(assets/images/icons/heart_blue.svg)' },
    { name: '--star', value: 'url(assets/images/icons/star_blue.svg)' },
    { name: '--share', value: 'url(assets/images/icons/share_blue.svg)' },
    { name: '--content', value: 'url(assets/images/icons/content_blue.svg)' },
    { name: '--main-back-gradient', value: 'linear-gradient(180deg, #DDA839 -13.12%, #FFDF99 53.7%, #CC8630 115.63%)' },
    { name: '--main-back-stripes_md', value: 'url(assets/images/strips_long_md.svg)' },
    { name: '--main-back-stripes', value: 'url(assets/images/stripes_long_blue.svg)' },
    { name: '--venz_', value: 'url(assets/images/venz_b.png)' },
    { name: '--venz_cover', value: 'url(assets/images/ssr.png)' },
    { name: '--m-burg', value: 'url(assets/images/icons/m-burg_b.svg)' },
    { name: '--m-books', value: 'url(assets/images/icons/m-books_b.svg)' },
    { name: '--m-prs', value: 'url(assets/images/icons/m-prs_b.svg)' },
    { name: '--m-srch', value: 'url(assets/images/icons/m-srch_b.svg)' },
    { name: '--m-pl', value: 'url(assets/images/icons/m-pl_b.svg)' },
    { name: '--m-pl-swch', value: 'url(assets/images/icons/m-pl-swch_b.svg)' },
    { name: '--shoffl', value: 'url(assets/images/icons/shofl_bl.svg)' },
    { name: '--no-shfl', value: 'url(assets/images/icons/no_shfl_bl.svg)' },
    { name: '--30sback', value: 'url(assets/images/icons/30sback_bl.svg)' },
    { name: '--30sforw', value: 'url(assets/images/icons/30sff_bl.svg)' },
    { name: '--no-repeat', value: 'url(assets/images/icons/no-repit_bl.svg)' },
    { name: '--all-repeat', value: 'url(assets/images/icons/all-repeat_bl.svg)' },
    { name: '--one-repeat', value: 'url(assets/images/icons/one-repeat_bl.svg)' },
    { name: '--side-back-stripes', value: 'url(assets/images/top_strips_blue.svg)' },
    { name: '--close_butn', value: 'url(assets/images/icons/close_blue.svg)' },
    { name: '--1speed', value: 'url(assets/images/icons/1x_b.svg)' },
    { name: '--l-share', value: 'url(assets/images/icons/l-share_b.svg)' },
    { name: '--add-p', value: 'url(assets/images/icons/add-p_b.svg)' },
    { name: '--l-heart', value: 'url(assets/images/icons/l-heart_b.svg)' },
    { name: '--lk-heart', value: 'url(assets/images/icons/lk-heart_b.svg)' },
    { name: '--scroll_top', value: 'url(assets/images/icons/up_b.svg)' },
    { name: '--l-star', value: 'url(assets/images/icons/l-star_b.svg)' },
    { name: '--listt_', value: 'url(assets/images/icons/listt_b.svg)' },
    { name: '--selection', value: 'rgba(211, 245, 254, 1)' },
    { name: '--check', value: 'url(assets/images/icons/chck_blue.svg)' },
    { name: '--border', value: 'rgba(211, 245, 254, 1)' },
    { name: '--side_back', value: 'rgba(211, 245, 254, 1)' },
    { name: '--shadow', value: 'rgba(0, 57, 100, 0.5)' },
    { name: '--blockquote-par', value: 'rgba(42, 124, 187, 1)' },
    { name: '--blockquote-before', value: 'url(assets/images/icons/trishul_blue.svg)' },
    { name: '--blockquote-after', value: 'url(assets/images/icons/line_blue.svg)' },
    { name: '--blockquote-after_md', value: 'url(assets/images/icons/line_md_blue.svg)' },
    { name: '--copy_', value: 'url(assets/images/icons/copy_highlight_blue.svg)' },
    { name: '--shr_', value: 'url(assets/images/icons/share_highlight_blue.svg)' },
    { name: '--add_', value: 'url(assets/images/icons/fav_highlight_blue.svg)' },
    { name: '--srch_', value: 'url(assets/images/icons/search_highlight_blue.svg)' },
    { name: '--burger', value: 'url(assets/images/icons/burg_blue.svg)' },
    { name: '--arrow_d', value: 'url(assets/images/icons/arr_dd_blue.svg)' },
    { name: '--arrow_w', value: 'url(assets/images/icons/arr_w_blue.svg)' },
    { name: '--triangl_w', value: 'url(assets/images/icons/triangl_blue.svg)' },
    { name: '--buttn_catg', value: 'url(assets/images/icons/buttn_catg_bl.svg)' },
    { name: '--menu_font', value: 'rgba(137, 87, 10, 1)' },
    { name: '--serch', value: 'url(assets/images/icons/serch_blue.svg)' },
    { name: '--btt_top', value: 'url(assets/images/icons/btt_top_blue.svg)' },
    { name: '--signin', value: 'url(assets/images/icons/signin_blue.svg)' },
    { name: '--authorized', value: 'url(assets/images/icons/ava_outer_blue.svg)' },
    { name: '--circl_lang', value: 'url(assets/images/icons/circle_blue.svg)' },
    { name: '--circl_lt', value: 'url(assets/images/icons/light_blue.svg)' },
    { name: '--circl_md', value: 'url(assets/images/icons/bw_blue.svg)' },
    { name: '--circl_dk', value: 'url(assets/images/icons/dark_blue.svg)' },
    { name: '--lan_switch', value: 'url(assets/images/icons/lan_switch_blue.svg)' },
    { name: '--lan_btn', value: 'url(assets/images/icons/lang_blue.svg)' },
    { name: '--ar', value: 'url(assets/images/icons/ar_blue.svg)' },
    { name: '--bf_gradient', value: 'linear-gradient(180deg, #F9F5D1 0%, rgba(255, 255, 255, 0) 100%)' },
    { name: '--blur_main', value: 'rgba(0, 57, 100, 0.55)' },
    { name: '--blur_sec', value: 'rgba(0, 57, 100, 0.5)' },
    { name: '--btn_main', value: 'url(assets/images/icons/btr_reg_blue.svg)' },
    { name: '--btn_main_r', value: 'url(assets/images/icons/btr_reg_b_blue.svg)' },
    { name: '--t1', value: 'url(assets/images/icons/t1_blue.svg)' },
    { name: '--y1', value: 'url(assets/images/icons/yt1_blue.svg)' },
    { name: '--v1', value: 'url(assets/images/icons/vk1_blue.svg)' },
    { name: '--f1', value: 'url(assets/images/icons/f1_blue.svg)' },
    { name: '--i1', value: 'url(assets/images/icons/i1.svg)' },
    { name: '--t2', value: 'url(assets/images/icons/t2_blue.svg)' },
    { name: '--m1', value: 'url(assets/images/icons/m1_blue.svg)' },
    { name: '--bt', value: 'url(assets/images/bt_blue.svg)' },
    { name: '--x_bt', value: 'url(assets/images/icons/x_blue.svg)' },
    { name: '--x_a', value: 'url(assets/images/icons/x_a_blue.svg)' },
    { name: '--arrs_sh', value: 'url(assets/images/icons/arrs_blue.svg)' },
    { name: '--lib-after', value: 'url(assets/images/l_line_blue.svg)' },
    { name: '--tab_nominal', value: 'url(assets/images/tab_noml_blue.svg)' },
    { name: '--tab_active', value: 'url(assets/images/tab_selected_blue.svg)' },
    { name: '--lib-after_md', value: 'url(assets/images/lib-after_md_blue.svg)' },
    { name: '--book_about', value: '#014c86' },
    { name: '--buttn_catg2', value: 'url(assets/images/buttn_catg2_blue.svg)' },
    { name: '--om_main', value: 'url(assets/images/icons/om_main_blue.svg)' },
    { name: '--nav_cont_sch', value: 'url(assets/images/icons/search_md_blue.svg)' },
    { name: '--burger_md', value: 'url(assets/images/icons/burg_md_blue.svg)' },
    { name: '--pl_bg', value: 'url(assets/images/icons/pl_bg-b.svg)' },
    { name: '--ps_bg', value: 'url(assets/images/icons/ps_bg-b.svg)' },
    { name: '--d_line', value: '#72B8ED' },
    { name: '--p_line', value: '#075A9B' },
    { name: '--d_back', value: 'rgba(240, 197, 122, 1)' },
    { name: '--tri_', value: 'url(assets/images/icons/tri_blue.svg)' },
    { name: '--pl_start', value: 'rgba(211, 245, 254, 1)' },
    { name: '--pl_stop', value: 'rgba(42, 124, 187, 1)' },
    { name: '--pl_start1', value: '#5CB1F3' },
    { name: '--pl_stop1', value: '#1867A4' },
    { name: '--pl_start2', value: '#2A7CBB' },
    { name: '--pl_stop2', value: '#115D97' },
    { name: '--pl_line', value: '#fff' },
    { name: '--pl_line1', value: 'rgba(114, 184, 237, 1)' },
    { name: '--pl-back-gradient', value: 'linear-gradient(90deg, #4495D4 0%, #075896 50%, #5CACEA 100%)' },
    { name: '--line_back', value: 'rgba(105, 187, 250, 1)' },
    { name: '--line_back1', value: 'rgba(7, 90, 155, 1)' },
    { name: '--st_back', value: 'rgba(42, 124, 187, 1)' },
    { name: '--line_handle', value: '#fff' },
    { name: '--line_handle1', value: 'rgba(42, 124, 187, 1)' },
    { name: '--before_handle', value: '#1162a1' },
    { name: '--btt_gradient', value: 'linear-gradient(to right, #fff, #fff), linear-gradient(to right, rgba(42, 124, 187, 1), rgba(42, 124, 187, 1))' },
    { name: '--cursor-move', value: 'url(assets/images/icons/movee_b.svg)' },
    { name: '--menu_font1', value: 'rgba(7, 90, 155, 1)' },
    { name: '--menu_font2', value: 'rgba(137, 87, 10, 1)' },
    { name: '--nav-pl', value: 'url(assets/images/icons/nav-pl_bl.svg)' },

    { name: '--button_hover_bg_sm', value: 'url(assets/images/button/button-hover-bg-blue-sm.webp)' },
    { name: '--button_hover_bg_md', value: 'url(assets/images/button/button-hover-bg-blue-md.webp)' },
    { name: '--button_hover_bg_lg', value: 'url(assets/images/button/button-hover-bg-blue-lg.webp)' },
    { name: '--button_hover_bg_xl', value: 'url(assets/images/button/button-hover-bg-blue-xl.webp)' },
    { name: '--button_hover_bg_xxl', value: 'url(assets/images/button/button-hover-bg-blue-xxl.webp)' },
    { name: '--button_hover_bg_xxxl', value: 'url(assets/images/button/button-hover-bg-blue-xxxl.webp)' },
    { name: '--button_sm', value: 'url(assets/images/button/button-bg-sm.webp)' },
    { name: '--button_md', value: 'url(assets/images/button/button-bg-md.webp)' },
    { name: '--button_lg', value: 'url(assets/images/button/button-bg-lg.webp)' },
    { name: '--button_xl', value: 'url(assets/images/button/button-bg-xl.webp)' },
    { name: '--button_xxl', value: 'url(assets/images/button/button-bg-xxl.webp)' },
    { name: '--button_xxxl', value: 'url(assets/images/button/button-bg-xxl.webp)' },
    { name: '--ard', value: 'url(assets/images/icons/ard_b.svg)' },
  ];
  darkThemeColors: Array<any> = [
    { name: '--grey-back', value: '#303439' },
    { name: '--dr-back', value: '#111' },
    { name: '--drf-back', value: '#333' },
    { name: '--main-text', value: '#fff' },
    { name: '--main-background-image', value: 'url(assets/images/Pat_50.svg), url(assets/images/side_gradient.svg)' },
    { name: '--top-cover', value: 'url(assets/images/gradient.svg)' },
    { name: '--cover_telegram', value: 'url(assets/images/icons/telg.svg)' },
    { name: '--cover_instagram', value: 'url(assets/images/icons/instg.svg)' },
    { name: '--cover_phone', value: 'url(assets/images/icons/phone.svg)' },
    { name: '--cover_email', value: 'url(assets/images/icons/mail.svg)' },
    { name: '--home-image', value: 'url(assets/images/icons/home.svg)' },
    { name: '--heart_dark', value: 'url(assets/images/icons/like_dark.svg)' },
    { name: '--button_figure', value: 'url(assets/images/icons/button_hover.svg)' },
    { name: '--button_', value: 'url(assets/images/icons/button_.svg)' },
    { name: '--star_dark', value: 'url(assets/images/icons/star_dark.svg)' },
    { name: '--font-color', value: 'rgba(83, 46, 0, 1)' },
    { name: '--font-color1', value: 'rgba(83, 46, 0, 1)' },
    { name: '--text-color', value: 'rgba(222, 165, 61, 1)' },
    { name: '--light-color', value: 'rgba(249, 233, 200, 1)' },
    { name: '--text-color23', value: 'rgba(83, 46, 0, 1)' },
    { name: '--calendar', value: 'url(assets/images/icons/calendar.svg)' },
    { name: '--clock', value: 'url(assets/images/icons/clock.svg)' },
    { name: '--heart', value: 'url(assets/images/icons/heart.svg)' },
    { name: '--star', value: 'url(assets/images/icons/star.svg)' },
    { name: '--share', value: 'url(assets/images/icons/share.svg)' },
    { name: '--content', value: 'url(assets/images/icons/content.svg)' },
    { name: '--main-back-gradient', value: 'linear-gradient(180deg, #F8C760 -13.12%, #CC8630 53.7%, #F1B94F 115.63%)' },
    { name: '--main-back-stripes_md', value: 'url(assets/images/strips_long_md.svg)' },
    { name: '--main-back-stripes', value: 'url(assets/images/strips_long.svg)' },
    { name: '--venz_', value: 'url(assets/images/venz_.png)' },
    { name: '--venz_cover', value: 'url(assets/images/ssr.png)' },
    { name: '--m-burg', value: 'url(assets/images/icons/m-burg.svg)' },
    { name: '--m-books', value: 'url(assets/images/icons/m-books.svg)' },
    { name: '--m-prs', value: 'url(assets/images/icons/m-prs.svg)' },
    { name: '--m-srch', value: 'url(assets/images/icons/m-srch.svg)' },
    { name: '--m-pl', value: 'url(assets/images/icons/m-pl.svg)' },
    { name: '--m-pl-swch', value: 'url(assets/images/icons/m-pl-swch.svg)' },
    { name: '--shoffl', value: 'url(assets/images/icons/shoffl.svg)' },
    { name: '--no-shfl', value: 'url(assets/images/icons/no_shfl.svg)' },
    { name: '--30sback', value: 'url(assets/images/icons/30sback.svg)' },
    { name: '--30sforw', value: 'url(assets/images/icons/30sff.svg)' },
    { name: '--no-repeat', value: 'url(assets/images/icons/no-repit.svg)' },
    { name: '--all-repeat', value: 'url(assets/images/icons/all-repeat.svg)' },
    { name: '--one-repeat', value: 'url(assets/images/icons/one-repeat.svg)' },
    { name: '--side-back-stripes', value: 'url(assets/images/top_strips.svg)' },
    { name: '--1speed', value: 'url(assets/images/icons/1x.svg)' },
    { name: '--l-share', value: 'url(assets/images/icons/l-share.svg)' },
    { name: '--add-p', value: 'url(assets/images/icons/add-p.svg)' },
    { name: '--l-star', value: 'url(assets/images/icons/l-star.svg)' },
    { name: '--l-heart', value: 'url(assets/images/icons/l-heart.svg)' },
    { name: '--lk-heart', value: 'url(assets/images/icons/lk-heart.svg)' },
    { name: '--scroll_top', value: 'url(assets/images/icons/up_.svg)' },
    { name: '--close_butn', value: 'url(assets/images/icons/close.svg)' },
    { name: '--listt_', value: 'url(assets/images/icons/listt_.svg)' },
    { name: '--selection', value: 'rgba(255, 226, 163, 1)' },
    { name: '--check', value: 'url(assets/images/icons/chck.svg)' },
    { name: '--border', value: 'rgba(243, 213, 147, 1)' },
    { name: '--side_back', value: 'rgba(253, 237, 201, 1)' },
    { name: '--shadow', value: 'rgba(83, 46, 0, 0.6)' },
    { name: '--blockquote-par', value: 'rgba(211, 146, 60, 1)' },
    { name: '--blockquote-before', value: 'url(assets/images/icons/trishul.svg)' },
    { name: '--blockquote-after', value: 'url(assets/images/icons/line.svg)' },
    { name: '--blockquote-after_md', value: 'url(assets/images/icons/line_md.svg)' },
    { name: '--copy_', value: 'url(assets/images/icons/copy_highlight.svg)' },
    { name: '--shr_', value: 'url(assets/images/icons/share_highlight.svg)' },
    { name: '--add_', value: 'url(assets/images/icons/fav_highlight.svg)' },
    { name: '--srch_', value: 'url(assets/images/icons/search_highlight.svg)' },
    { name: '--burger', value: 'url(assets/images/icons/burg.svg)' },
    { name: '--arrow_d', value: 'url(assets/images/icons/arr_dd.svg)' },
    { name: '--arrow_w', value: 'url(assets/images/icons/arr_w_.svg)' },
    { name: '--triangl_w', value: 'url(assets/images/icons/triangl_.svg)' },
    { name: '--buttn_catg', value: 'url(assets/images/icons/buttn_catg.svg)' },
    { name: '--menu_font', value: 'rgba(255, 255, 255, 1)' },
    { name: '--serch', value: 'url(assets/images/icons/serch.svg)' },
    { name: '--btt_top', value: 'url(assets/images/icons/btt_top.svg)' },
    { name: '--signin', value: 'url(assets/images/icons/signin.svg)' },
    { name: '--authorized', value: 'url(assets/images/icons/ava_outer.svg)' },
    { name: '--circl_lang', value: 'url(assets/images/icons/circle.svg)' },
    { name: '--circl_lt', value: 'url(assets/images/icons/light.svg)' },
    { name: '--circl_md', value: 'url(assets/images/icons/bw.svg)' },
    { name: '--circl_dk', value: 'url(assets/images/icons/dark.svg)' },
    { name: '--lan_switch', value: 'url(assets/images/icons/lan_switch.svg)' },
    { name: '--lan_btn', value: 'url(assets/images/icons/lang.svg)' },
    { name: '--ar', value: 'url(assets/images/icons/ar_light.svg)' },
    { name: '--bf_gradient', value: 'linear-gradient(180deg, #F1B94F 0%, rgba(255, 226, 163, 0) 100%)' },
    { name: '--blur_sec', value: 'rgba(137, 87, 10, 0.5)' },
    { name: '--blur_main', value: 'rgba(83, 46, 0, 0.55)' },
    { name: '--btn_main', value: 'url(assets/images/icons/btr_reg.svg)' },
    { name: '--btn_main_r', value: 'url(assets/images/icons/btr_reg_b.svg)' },
    { name: '--t1', value: 'url(assets/images/icons/t1.svg)' },
    { name: '--y1', value: 'url(assets/images/icons/yt1.svg)' },
    { name: '--v1', value: 'url(assets/images/icons/vk1.svg)' },
    { name: '--f1', value: 'url(assets/images/icons/f1.svg)' },
    { name: '--i1', value: 'url(assets/images/icons/i1_blue.svg)' },
    { name: '--t2', value: 'url(assets/images/icons/t2.svg)' },
    { name: '--m1', value: 'url(assets/images/icons/m1.svg)' },
    { name: '--bt', value: 'url(assets/images/bt_.svg)' },
    { name: '--x_bt', value: 'url(assets/images/icons/x_.svg)' },
    { name: '--x_a', value: 'url(assets/images/icons/x_a.svg)' },
    { name: '--arrs_sh', value: 'url(assets/images/icons/arrs.svg)' },
    { name: '--lib-after', value: 'url(assets/images/l_line.svg)' },
    { name: '--tab_nominal', value: 'url(assets/images/tab_noml.svg)' },
    { name: '--tab_active', value: 'url(assets/images/tab_selected.svg)' },
    { name: '--lib-after_md', value: 'url(assets/images/lib-after_md.svg)' },
    { name: '--book_about', value: '#dea53d' },
    { name: '--buttn_catg2', value: 'url(assets/images/buttn_catg2.svg)' },
    { name: '--om_main', value: 'url(assets/images/icons/om_main.svg)' },
    { name: '--nav_cont_sch', value: 'url(assets/images/icons/search_md.svg)' },
    { name: '--burger_md', value: 'url(assets/images/icons/burg_md.svg)' },
    { name: '--pl_bg', value: 'url(assets/images/icons/pl_bg.svg)' },
    { name: '--ps_bg', value: 'url(assets/images/icons/ps_bg.svg)' },
    { name: '--d_back', value: 'rgb(231 174 78)' },
    { name: '--d_line', value: '#F3D593' },
    { name: '--p_line', value: '#9A6119' },
    { name: '--tri_', value: 'url(assets/images/icons/tri_.svg)' },
    { name: '--pl_start', value: 'rgba(255, 226, 163, 1)' },
    { name: '--pl_stop', value: 'rgba(240, 187, 86, 1)' },
    { name: '--pl_start1', value: '#E1A644' },
    { name: '--pl_stop1', value: '#9A6119' },
    { name: '--pl_start2', value: '#B87E2B' },
    { name: '--pl_stop2', value: '#86510E' },
    { name: '--pl_line', value: 'rgba(243, 213, 147, 1)' },
    { name: '--pl_line1', value: 'rgba(243, 213, 147, 1)' },
    { name: '--pl-back-gradient', value: 'linear-gradient(90deg, #DEA53D 0%, #D08C33 50%, #F0BB56 100%)' },
    { name: '--line_back', value: 'rgba(154, 97, 25, 1)' },
    { name: '--line_back1', value: 'rgba(154, 97, 25, 1)' },
    { name: '--st_back', value: 'rgba(154, 97, 25, 1)' },
    { name: '--line_handle', value: 'rgba(255, 222, 153, 1)' },
    { name: '--line_handle1', value: 'rgba(225, 166, 68, 1)' },
    { name: '--before_handle', value: '#d39135' },
    { name: '--btt_gradient', value: 'linear-gradient(to right, #fff, #fff), linear-gradient(to right, #FCE1B3, #D19036)' },
    { name: '--cursor-move', value: 'url(assets/images/icons/movee_.svg)' },
    { name: '--menu_font1', value: 'rgba(137, 87, 10, 1)' },
    { name: '--menu_font2', value: '#fff' },
    { name: '--nav-pl', value: 'url(assets/images/icons/nav-pl.svg)' },

    { name: '--button_hover_bg_sm', value: 'url(assets/images/button/button-hover-bg-gold-sm.webp)' },
    { name: '--button_hover_bg_md', value: 'url(assets/images/button/button-hover-bg-gold-md.webp)' },
    { name: '--button_hover_bg_lg', value: 'url(assets/images/button/button-hover-bg-gold-lg.webp)' },
    { name: '--button_hover_bg_xl', value: 'url(assets/images/button/button-hover-bg-gold-xl.webp)' },
    { name: '--button_hover_bg_xxl', value: 'url(assets/images/button/button-hover-bg-gold-xxl.webp)' },
    { name: '--button_hover_bg_xxxl', value: 'url(assets/images/button/button-hover-bg-gold-xxxl.webp)' },
    { name: '--button_sm', value: 'url(assets/images/button/button-bg-sm.webp)' },
    { name: '--button_md', value: 'url(assets/images/button/button-bg-md.webp)' },
    { name: '--button_lg', value: 'url(assets/images/button/button-bg-lg.webp)' },
    { name: '--button_xl', value: 'url(assets/images/button/button-bg-xl.webp)' },
    { name: '--button_xxl', value: 'url(assets/images/button/button-bg-xxl.webp)' },
    { name: '--button_xxxl', value: 'url(assets/images/button/button-bg-xxl.webp)' },
    { name: '--ard', value: 'url(assets/images/icons/ard.svg)' },
  ];
  isThemeSwitchClicked: boolean = false;
  isLangSwitchClicked: boolean = false;
  private destroy$ = new Subject<void>();

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private toasterService: ToasterService,
    private location: Location,
    private renderer: Renderer2,
    @Inject(DOCUMENT) private document: Document,
    private spinnerService: SpinnerService,
    private scrollManagementService: ScrollManagementService
  ) {
    if (isPlatformBrowser(this.platformId)) {
      window.addEventListener('unhandledrejection', event => {
        if (event.reason && event.reason.message &&
          event.reason.message.includes('Loading chunk')) {
          window.location.reload();
        }
      });
    }

    this.shareDataService.showInfoModal$.pipe(takeUntil(this.destroy$)).subscribe((mess) => {
      if (mess) {
        this.openModal('Вы не авторизованы')
      }
    })
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  closeSide() {
    this.isThemeSwitchClicked = false;
  }

  ngOnInit() {
    // Load saved theme from localStorage
    this.loadSavedTheme();

    // Initial language setup
    const urlLang = this.router.url.split('/')[1];
    if (urlLang && ['ru', 'en', 'de'].includes(urlLang)) {
      this.currentLanguage.set(urlLang);
      this.translocoService.setActiveLang(urlLang);

      // Load translations for initial language
      this.loadTranslations(urlLang);
    }

    // Listen to route changes
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      const urlLang = this.router.url.split('/')[1];
      if (urlLang && ['ru', 'en', 'de'].includes(urlLang)) {
        this.currentLanguage.set(urlLang);
        //this.translocoService.setActiveLang(urlLang); закомментировал, так как вызывает дублирование событий langChanges

        // Load translations when language changes
        this.loadTranslations(urlLang);
      }
    });
  }

  // Helper method to load translations
  private loadTranslations(lang: string) {
    // Inject TranslationService at the top of the component
    // this.translationService.getTranslation(lang).subscribe({
    //   next: () => {
    //   },
    //   error: (err) => {
    //     console.error(`Error loading translations for ${lang}:`, err);
    //   }
    // });
  }

  // Load saved theme from localStorage
  private loadSavedTheme(): void {
    if (isPlatformBrowser(this.platformId)) {
      try {
        const savedTheme = localStorage.getItem('selectedTheme');
        if (savedTheme && this.isValidTheme(savedTheme)) {
          this.themeToSet = savedTheme;
        } else {
          this.themeToSet = ThemeEnum.Light;
        }
        setTimeout(() => {
          this.setTheme();
        }, 0);
      } catch (error) {
        console.warn('Failed to load theme from localStorage:', error);
        // Fallback to default Light theme
        this.themeToSet = ThemeEnum.Light;
        setTimeout(() => {
          this.setTheme();
        }, 0);
      }
    } else {
      this.themeToSet = ThemeEnum.Light;
    }
  }

  // Validate if the theme value is a valid ThemeEnum value
  private isValidTheme(theme: string): theme is ThemeEnum {
    return Object.values(ThemeEnum).includes(theme as ThemeEnum);
  }

  // Save theme to localStorage
  private saveThemeToStorage(theme: ThemeEnum): void {
    if (isPlatformBrowser(this.platformId)) {
      try {
        localStorage.setItem('selectedTheme', theme);
      } catch (error) {
        console.warn('Failed to save theme to localStorage:', error);
      }
    }
  }

  currentLanguage = signal<string>('ru');

  changeLanguage(lan: string) {
    const currentUrl = this.router.url;
    const newUrl = currentUrl.replace(
      `/${this.currentLanguage()}/`,
      `/${lan}/`
    );
    // this.translocoService.setActiveLang(newLang);
    // this.currentLanguage.set(newLang);
    this.router.navigate([newUrl]);
  }

  openMenu(i: number) {
    this.isMenuOpened[i] = !this.isMenuOpened[i];
  }



  navigateToLibrary() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/library`]);
    this.isSidebarOpen = false;
    this.setBody();
  }

  navigateToPhoto() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/photo`]);
    this.isSidebarOpen = false;
    this.setBody();
  }

  navigateToCategorie() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/categories`]);
    this.isSidebarOpen = false;
    this.setBody();
  }

  navigateToAudio() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/audiogallery/audiolektsii`]);
    this.isSidebarOpen = false;
    this.setBody();
  }

  navigateToForum() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/forum`]);
    this.isSidebarOpen = false;
    this.setBody();
  }

  goBack(): void {
    if (isPlatformBrowser(this.platformId)) {
      if (window.history.length > 1) {
        this.location.back();
      } else {
        this.router.navigate(['/']);
      }
    } else {
      console.log('Back button not available in SSR.');
    }
  }

  changeCSSProperty(name: string, value: string) {
    if (isPlatformBrowser(this.platformId)) {
      document.documentElement.style.setProperty(name, value);
    }
  }

  ngAfterViewInit() {
    if (isPlatformBrowser(this.platformId)) {
      window.onload = function () {
        (document.querySelector('.preloader') as HTMLElement).style.display = 'none';
      }
    }
  }

  setTheme() {
    switch (this.themeToSet) {
      case ThemeEnum.Light:
        this.lightThemeColors.forEach((i) => {
          this.setThemeStyles(i.name, i.value);
        });
        this.theme = 'Light';
        break;
      case ThemeEnum.lightBlue:
        this.lightBlueThemeColors.forEach((i) => {
          this.setThemeStyles(i.name, i.value);
        });
        this.theme = 'lightBlue';
        break;
      case ThemeEnum.Dark:
        this.darkThemeColors.forEach((i) => {
          this.setThemeStyles(i.name, i.value);
        });
        this.theme = 'Dark';
        break;
    }
  }

  setThemeStyles(name: string, value: string) {
    document.documentElement.style.setProperty(name, value);
  }

  onThemeChange(theme: ThemeEnum | { value: ThemeEnum } | string) {
    let themeValue: string;

    // Handle different input formats
    if (typeof theme === 'object' && theme.value) {
      themeValue = theme.value;
    } else if (typeof theme === 'string') {
      themeValue = theme;
    } else {
      return; // Invalid theme format
    }

    // Validate theme value
    if (this.isValidTheme(themeValue)) {
      this.themeToSet = themeValue;

      if (isPlatformBrowser(this.platformId)) {
        this.setTheme();
        // Save theme to localStorage
        this.saveThemeToStorage(themeValue as ThemeEnum);
      }
    }
  }

  setBody() {
    if (isPlatformBrowser(this.platformId)) {
      const body = this.document.body;
      if (this.isSidebarOpen) {
        this.renderer.setStyle(body, 'overflow', 'hidden');
      } else {
        if (window.innerWidth <= 500 && (this.isThemeSwitchClicked || this.isLangSwitchClicked)) {
          return;
        } else {
          this.renderer.setStyle(body, 'overflow', 'auto');
        }
      }
    }
  }

  toggleSidebar(event: boolean) {
    this.isSidebarOpen = event;
    this.setBody();
  }

  showSuccess() {
    this.toasterService.showToast('Operation successful!', 'success', 'bottom-middle', 10000);
  }

  showError() {
    this.toasterService.showToast('Something went wrong!', 'error', 'bottom-middle');
  }

  openSitemap() {
    // const lang = this.translocoService.getActiveLang();
    // this.router.navigate([`/${lang}/sitemap`]);
    this.isSidebarOpen = false;
    this.setBody();
  }

  logout() {
    this.authService.logout();
  }
}
