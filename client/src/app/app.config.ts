import {provideHttpClient, withFetch, withInterceptors} from '@angular/common/http';
import { ApplicationConfig, provideZoneChangeDetection, isDevMode } from '@angular/core';
import { provideClientHydration, withEventReplay, withHttpTransferCacheOptions } from '@angular/platform-browser';
import { apiInterceptor } from "./interceptors/api.interceptor";
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling, withPreloading, PreloadAllModules } from '@angular/router';
import { routes } from './app.routes';
import { TranslocoHttpLoader } from './transloco-loader';
import { provideTransloco } from '@jsverse/transloco';
import { provideAnimations } from '@angular/platform-browser/animations';
import {stateInterceptor} from "@/interceptors/state.interceptor";
import {authInterceptor} from "@/interceptors/auth.interceptor";
import { TranslationService } from './services/translation.service';
import { provideScReCaptchaSettings } from '@semantic-components/re-captcha';
import { IMAGE_CONFIG } from '@angular/common';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes,
      withPreloading(PreloadAllModules),
      withEnabledBlockingInitialNavigation(),
      withInMemoryScrolling({
        scrollPositionRestoration: 'top',
        anchorScrolling: 'enabled'
      })
    ),
    provideAnimations(),
    provideClientHydration(
      withEventReplay(),
      withHttpTransferCacheOptions({
        includePostRequests: true,
        includeRequestsWithAuthHeaders: false,
        filter: (req) => {
          // Cache all GET and HEAD requests except those with auth headers
          return ['GET', 'HEAD'].includes(req.method) &&
                 !req.headers.has('Authorization') &&
                 !req.headers.has('Proxy-Authorization');
        }
      })
    ),
    provideHttpClient(withFetch(), withInterceptors([apiInterceptor, stateInterceptor, authInterceptor])),
    provideTransloco({
      config: {
        availableLangs: ['ru', 'en', 'de'],
        defaultLang: 'ru',
        reRenderOnLangChange: true,
        prodMode: !isDevMode(),
        fallbackLang: 'ru'
      },
      loader: TranslocoHttpLoader
    }),
    TranslationService,
    provideScReCaptchaSettings({
      v2SiteKey: '6LdHg4IrAAAAAP713d1wv8qRL0xZuz8HeRSscY4P',
      v3SiteKey: '6LflboIrAAAAAEdtMpcEIraBhkGqfqVe0NBiqCa7',
      languageCode: 'ru',
    }),
    {
      provide: IMAGE_CONFIG,
      useValue: {
        disableImageSizeWarning: true,
        disableImageLazyLoadWarning: true,
        placeholderResolution: 40
      }
    }
  ]
};
